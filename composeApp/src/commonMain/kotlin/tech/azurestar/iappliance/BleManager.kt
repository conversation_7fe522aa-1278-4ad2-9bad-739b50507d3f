package tech.azurestar.iappliance

import com.juul.kable.Peripheral
import com.juul.kable.PeripheralBuilder
import com.juul.kable.Scanner
import com.juul.kable.State
import com.juul.kable.characteristicOf
import com.juul.kable.peripheral
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlin.uuid.ExperimentalUuidApi
import kotlin.uuid.Uuid

class BleManager(private val coroutineScope: CoroutineScope = CoroutineScope(Dispatchers.Default)) {

    private val serviceUuidString = "12345678-1234-1234-1234-123456789abc"
    private val characteristicUuidString = "12345678-1234-1234-1234-123456789abd"

    private val _logs = MutableStateFlow<List<String>>(emptyList())
    val logs: Flow<List<String>> = _logs.asStateFlow()

    private var peripheral: Peripheral? = null
    private var packetCount = 0

    init {
        connectAndSubscribe()
    }

    @OptIn(ExperimentalUuidApi::class)
    private fun connectAndSubscribe() {
        coroutineScope.launch {
            try {
                addLog("Starting BLE scan for service: $serviceUuidString")
                println("Starting BLE scan for service: $serviceUuidString")

                val scanner = Scanner()
                println("Starting BLE scan for service: $serviceUuidString")
                scanner
                    .advertisements.collect {
                        val advertisement = it

                        println("advertisement: $advertisement")

                        if(!advertisement.uuids.contains(Uuid.parse(serviceUuidString)))
                            return@collect

                        println("advertisement nice: $advertisement")

                        addLog("Found device: ${advertisement.name ?: "Unknown"}")

                        peripheral = Peripheral(advertisement) {
                            onServicesDiscovered {
                                addLog("Services discovered")
                                launch { subscribeToCharacteristic() }
                            }
                        }

                        peripheral?.connect()
                        addLog("Connected to device")
                    }
            } catch (e: Exception) {
                println("Error: ${e.message}")
                addLog("Error: ${e.message}")
            }
        }
    }

    @OptIn(ExperimentalUuidApi::class)
    private suspend fun subscribeToCharacteristic() {
        addLog("Starting subscription to characteristic")
        try {
            val characteristic = characteristicOf(
                Uuid.parse(serviceUuidString), Uuid.parse(
                    characteristicUuidString
                )
            )
            addLog("Characteristic found, observing...")
            peripheral?.observe(characteristic)?.collect { data ->
                packetCount++
                try {
                    val stringData = data.decodeToString()
                    addLog("Packet $packetCount: $stringData")
                } catch (e: Exception) {
                    addLog("Received packet $packetCount with ${data.size} bytes")
                }
            }
        } catch (e: Exception) {
            addLog("Subscription error: ${e.message}")
        }
    }

    private fun addLog(message: String) {
        _logs.value = _logs.value + message
    }

    fun disconnect() {
        coroutineScope.launch {
            peripheral?.disconnect()
            addLog("Disconnected")
        }
    }
}